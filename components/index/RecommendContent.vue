<template>
  <!-- 加载状态 -->
  <view v-if="loading && !hotVideos.length" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载数据...</text>
  </view>

  <!-- 错误提示 -->
  <view v-else-if="error && !hotVideos.length" class="error-container">
    <text class="error-text">{{ error }}</text>
    <view class="error-retry" @click="loadData">点击重试</view>
  </view>

  <!-- 热门视频 -->
  <view v-else class="box">
    <view class="title">热门视频</view>
    <HorizontalVideoList :videoList="hotVideos" />
  </view>

  <!-- 各类型视频内容，使用循环生成 -->
  <view v-for="(category, index) in categories" :key="index" class="box">
    <view class="title">
      <view class="title-link" @click="navigateToTab(category)">{{ category }} <i class="icon youjiantou"></i></view>
    </view>

    <!-- 如果有数据，根据类型显示不同的组件 -->
    <template v-if="categoryVideos[category] && categoryVideos[category].length > 0">
      <!-- 横屏视频列表 -->
      <HorizontalVideoList v-if="isCategoryHorizontal(category)" :videoList="categoryVideos[category]" />
      <!-- 竖屏视频列表 -->
      <VerticalVideoList v-else :videoList="categoryVideos[category]" />
    </template>

    <!-- 如果没有数据，显示提示 -->
    <view v-else class="no-data-tip">
      <text>暂无{{ category }}数据</text>
    </view>

    <view class="more">
      <span @click="navigateToTab(category)"><i class="icon more"></i>更多{{ category }}</span>
      <span class="line"></span>
      <span @click="refreshVideos(category)" :class="{ 'refreshing': categoryVideos[`${category}Refreshing`] }">
        <i class="icon huanyipi" :class="{ 'rotating': categoryVideos[`${category}Refreshing`] }"></i>
        {{ categoryVideos[`${category}Refreshing`] ? '加载中...' : '换一批' }}
      </span>
    </view>
  </view>

  <!-- 为你推荐部分 -->
  <view class="box">
    <view class="title">猜你喜欢</view>
    <view v-if="loading && !mixedGroups.length" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载推荐数据...</text>
    </view>

    <view v-else-if="error && !mixedGroups.length" class="error-container">
      <text class="error-text">{{ error }}</text>
      <view class="error-retry" @click="loadMixedRecommendedVideos">点击重试</view>
    </view>

    <!-- 混合布局：10个横屏 + 9个竖屏为一组 -->
    <view v-else-if="mixedGroups && mixedGroups.length > 0">
      <view v-for="(group, groupIndex) in mixedGroups" :key="groupIndex" class="mixed-group">
        <!-- 横屏视频部分 -->
        <view v-if="group.horizontal && group.horizontal.length > 0" class="horizontal-section">
          <HorizontalVideoList :videoList="group.horizontal" />
        </view>
        <view v-if="group.vertical && group.vertical.length > 0" class="vertical-section">
          <VerticalVideoList :videoList="group.vertical" />
        </view>
      </view>
    </view>

    <!-- 没有数据时显示提示 -->
    <view v-else class="no-data-tip">
      <text>暂无推荐数据</text>
      <view class="retry-button" @click="loadMixedRecommendedVideos()">点击重试</view>
    </view>

    <!-- 加载状态指示器 -->
    <view class="load-more-container">
      <view v-if="recommendLoading" class="loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载中...</text>
      </view>
      <view v-else-if="!recommendHasMore" class="no-more-data">没有更多数据了</view>
      <view v-else class="loading-hint">上拉加载更多</view>
    </view>
  </view>
</template>

<script>
import { getVideoList, getRandomVideos, parseVideoData } from '@/api/video';
import { getVideoTypeList, TypeDisplayType, parseVideoTypeData } from '@/api/videoType';
import { getVideoTagList, TagDisplayType, parseVideoTagData } from '@/api/videoTag';
import { getCachedNavigationList } from '@/api/navigationCache';
import VerticalVideoList from '@/components/common/VerticalVideoList.vue';
import HorizontalVideoList from '@/components/common/HorizontalVideoList.vue';
import logger from '@/utils/logger';

export default {
  name: 'RecommendContent',
  components: {
    VerticalVideoList,
    HorizontalVideoList
  },
  props: {
    videoType: {
      type: String,
      default: null
    },
    videoTypeId: {
      type: [String, Number],
      default: null
    }
  },
  computed: {
    // 计算左侧列内容与右侧列内容
    leftItems() {
      return this.getWaterfallItems().left;
    },
    rightItems() {
      return this.getWaterfallItems().right;
    }
  },
  data() {
    return {
      loading: false,
      error: null,
      hotVideos: [],
      recommendedVideos: [],
      videoTypes: [],
      videoTags: [],
      categoryVideos: {}, // 存储各类型视频的数据
      categories: [], // 存储栏目类型，从 API 获取
      recommendPage: 1, // 推荐视频当前页码
      recommendLoading: false, // 推荐视频加载状态
      recommendHasMore: true, // 推荐视频是否还有更多数据
      recommendPageSize: 20, // 推荐视频每页数量
      dataLoaded: false, // 标记数据是否已加载，避免重复加载

      // 横屏竖屏相关
      navigations: [], // 导航数据
      horizontalVideos: [], // 横屏视频数据
      verticalVideos: [], // 竖屏视频数据
      mixedGroups: [] // 混合组合数据（10个横屏+9个竖屏）
    };
  },
  created() {
    // 初始化数据
    this.categoryVideos = {};
    // 确保 dataLoaded 初始值为 false
    this.dataLoaded = false;

    // 注意：我们不在这里初始化 categoryVideos，因为模板中已经处理了空数据的情况
  },
  mounted() {
    // 在mounted钩子中不加载数据，避免与activated钩子重复加载
    // 数据加载将在activated钩子中进行
    // 添加事件监听器
    // 先移除可能存在的监听器，避免重复
    uni.$off('page-reach-bottom', this.handleReachBottom);
    // 监听页面触底事件
    uni.$on('page-reach-bottom', this.handleReachBottom);
  },

  // 组件被keep-alive缓存时，再次激活时触发
  activated() {
    // 先移除可能存在的监听器，避免重复
    uni.$off('page-reach-bottom', this.handleReachBottom);
    // 重新添加事件监听器
    uni.$on('page-reach-bottom', this.handleReachBottom);

    // 强制重新加载数据，确保切换标签时能正确加载
    // 完全重置组件状态
    this.loading = false;
    this.error = null;
    this.hotVideos = [];
    this.recommendedVideos = [];
    this.categoryVideos = {};
    this.recommendPage = 1;
    this.recommendLoading = false;
    this.recommendHasMore = true;
    this.dataLoaded = false;
    // 重置横屏竖屏数据
    this.navigations = [];
    this.horizontalVideos = [];
    this.verticalVideos = [];
    this.mixedGroups = [];
    // 强制重新加载数据
    this.loadData();
  },

  // 组件被keep-alive缓存时，即将切换出去时触发
  deactivated() {
    // 移除事件监听器，避免内存泄漏
    uni.$off('page-reach-bottom', this.handleReachBottom);
  },

  // 页面卸载时移除监听
  beforeDestroy() {
    // 移除监听
    uni.$off('page-reach-bottom', this.handleReachBottom);
  },


  methods: {
    /**
     * 加载首页数据
     */
    async loadData() {
      logger.log('开始加载推荐内容数据');
      // 仅在正在加载时阻止重复加载，其他情况均允许加载
      if (this.loading) {
        logger.log('正在加载中，不重复加载');
        return;
      }

      // 清空所有数据，确保每次加载都是全新的
      this.hotVideos = [];
      this.recommendedVideos = [];
      this.categoryVideos = {};
      this.horizontalVideos = [];
      this.verticalVideos = [];
      this.mixedGroups = [];

      try {
        // 设置加载状态
        this.loading = true;
        this.error = null;

        // 并行加载所有数据
        await this.loadAllData();

        // 标记数据已加载
        this.dataLoaded = true;
      } catch (error) {
        this.error = error.message || '加载数据失败';
        this.showErrorToast(this.error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 并行加载所有数据
     */
    async loadAllData() {
      // 先加载导航数据和视频类型
      await Promise.all([
        this.loadNavigations(),
        this.loadVideoTypes()
      ]);

      // 准备并行加载的任务
      const tasks = [];

      // 如果传入了特定的videoType，则只加载该类型的视频
      if (this.videoType && this.videoTypeId) {
        console.log(`使用传入的videoType: ${this.videoType}, videoTypeId: ${this.videoTypeId}加载数据`);
        // 这里可以根据传入的videoType和videoTypeId加载特定类型的视频
        // 目前RecommendContent组件不需要处理这种情况，因为它只在首页的推荐标签中使用
      } else {
        // 正常加载热门和推荐视频
        if (this.hotVideos.length === 0) {
          tasks.push(this.loadHotVideos());
        }

        // 加载混合推荐视频（替代原有的推荐视频）
        if (this.mixedGroups.length === 0) {
          tasks.push(this.loadMixedRecommendedVideos());
        }
      }

      // 加载各类型视频
      this.categoryVideos = {}; // 初始化分类视频数据对象

      // 为每个类别创建加载任务
      const categoryTasks = this.categories.map(category => this.loadCategoryData(category));
      tasks.push(...categoryTasks);

      // 并行执行所有任务
      await Promise.all(tasks);
    },

    /**
     * 加载单个类别的数据
     * @param {string} category - 类别名称
     */
    async loadCategoryData(category) {
      // 初始化为空数组
      this.$set(this.categoryVideos, category, []);

      try {
        // 找到对应的类型对象，获取API调用名称
        const typeObj = this.videoTypes.find(type => type.name === category);
        const apiCategoryName = typeObj ? typeObj.categotyName : category;

        const response = await getRandomVideos({
          maxResultCount: 6,
          skipCount: 0,
          videoType: apiCategoryName
        });

        if (this.isValidResponse(response)) {
          const parsedData = parseVideoData(response);
          this.$set(this.categoryVideos, category, parsedData);
        }
      } catch (error) {
        console.error(`加载 ${category} 类型数据失败:`, error);
        // 失败时保持空数组
      }
    },

    /**
     * 检查响应是否有效
     * @param {Object|Array} response - API响应
     * @returns {boolean} 是否有效
     */
    isValidResponse(response) {
      return (Array.isArray(response) && response.length > 0) ||
             (response && response.items && response.items.length > 0);
    },

    /**
     * 显示错误提示
     * @param {string} message - 错误信息
     */
    showErrorToast(message) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    },

    /**
     * 加载视频类型
     */
    async loadVideoTypes() {
      try {
        // 使用顶部导航栏的类型
        const { getNavigationList, filterMobileNavigations, convertToTabs } = await import('@/api/navigation');

        // 尝试从 API 获取导航数据
        const response = await getNavigationList();

        let tabs = [];

        if (response && Array.isArray(response)) {
          // 过滤移动端显示的导航项
          const mobileNavigations = filterMobileNavigations(response);

          // 转换为标签数据
          tabs = convertToTabs(mobileNavigations);
        } else {
          // 如果 API 请求失败，初始化为空数组
          tabs = [];

          // 显示错误提示
          uni.showToast({
            title: '加载导航数据失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }

        // 过滤推荐标签，只保留其他标签，但保留所有横屏和竖屏类型
        const filteredTabs = tabs.filter(tab => {
          // 排除推荐标签
          if (tab.name === '推荐' || !tab.categotyName) {
            return false;
          }

          // 保留所有其他类型（包括横屏和竖屏）
          return true;
        });

        // 使用标签的 name 作为显示名称，categotyName 作为API调用名称
        this.videoTypes = filteredTabs.map(tab => ({
          id: tab.categotyId,
          name: tab.name,           // 显示名称
          categotyName: tab.categotyName, // API调用名称
          extraParams: tab.extraParams
        }));

        // 更新栏目类型
        this.categories = this.videoTypes.map(type => type.name);
      } catch (error) {
        // 出错时初始化为空数组
        this.videoTypes = [];
        this.categories = [];

        // 显示错误提示
        uni.showToast({
          title: '加载视频类型失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 加载热门视频
     * 从 extraParams 为 "0" 的栏目中随机获取视频
     */
    async loadHotVideos() {
      try {
        // 设置需要获取的视频总数
        const totalVideosNeeded = 6;

        // 使用缓存服务获取导航数据
        const { getCachedNavigationList } = await import('@/api/navigationCache');
        const navigationResponse = await getCachedNavigationList();

        // 筛选 extraParams 中 isHorizontalLayout 为 true 的栏目
        const shortVideoCategories = navigationResponse
          .filter(nav => {
            if (!nav.isH5Display) return false;

            // 解析 extraParams
            let extraParamsObj = null;
            try {
              if (nav.extraParams && typeof nav.extraParams === 'string') {
                extraParamsObj = JSON.parse(nav.extraParams);
              }
            } catch (error) {
              console.error('解析extraParams失败:', error, nav.extraParams);
              return false;
            }

            // 筛选 isHorizontalLayout 为 true 的项目
            return extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          })
          .map(nav => nav.categotyName);

        // 如果没有找到 extraParams 为 "0" 的栏目，使用默认方式获取热门视频
        if (!shortVideoCategories.length) {
          // 使用默认方式获取热门视频
          const response = await getRandomVideos({
            maxResultCount: totalVideosNeeded,
            skipCount: 0,
            sorting: 'Random' // 使用随机排序
          });

          if (this.isValidResponse(response)) {
            this.hotVideos = parseVideoData(response);
          } else {
            this.hotVideos = [];
          }
          return;
        }

        // 随机打乱栏目顺序，增强随机性
        const shuffledCategories = this.shuffleArray(shortVideoCategories);

        // 使用所有栏目，不设置限制
        const categoriesToUse = shuffledCategories;

        // 计算每个栏目需要获取的视频数量
        // 如果栏目数量少于或等于需要的视频总数，每个栏目至少获发一个
        // 如果栏目数量多于需要的视频总数，则随机选择那么多个栏目
        let videosPerCategory;
        let categoriesToActuallyUse;

        if (categoriesToUse.length <= totalVideosNeeded) {
          // 每个栏目至少获取一个视频，剩下的平均分配
          videosPerCategory = Math.ceil(totalVideosNeeded / categoriesToUse.length);
          categoriesToActuallyUse = categoriesToUse;
        } else {
          // 随机选择totalVideosNeeded个栏目，每个获取一个视频
          videosPerCategory = 1;
          categoriesToActuallyUse = categoriesToUse.slice(0, totalVideosNeeded);
        }

        // 获取所有选中栏目的视频
        const allVideos = [];

        // 并行请求所有选中栏目的视频
        const requests = categoriesToActuallyUse.map(category => {
          return getRandomVideos({
            maxResultCount: videosPerCategory,
            skipCount: 0,
            videoType: category,
            sorting: 'Random' // 使用随机排序
          });
        });

        // 等待所有请求完成
        const responses = await Promise.all(requests);

        // 处理所有响应
        responses.forEach((response, index) => {
          if (this.isValidResponse(response)) {
            const parsedData = parseVideoData(response);
            allVideos.push(...parsedData);
          }
        });

        // 如果没有获取到视频，尝试使用默认方式
        if (allVideos.length === 0) {
          const response = await getRandomVideos({
            maxResultCount: totalVideosNeeded,
            skipCount: 0,
            sorting: 'Random' // 使用随机排序
          });

          if (this.isValidResponse(response)) {
            this.hotVideos = parseVideoData(response);
          } else {
            this.hotVideos = [];
          }
          return;
        }

        // 随机打乱并限制数量
        this.hotVideos = this.shuffleArray(allVideos).slice(0, totalVideosNeeded);
      } catch (error) {
        this.hotVideos = [];
        console.error('加载热门视频失败:', error);

        // 显示错误提示
        uni.showToast({
          title: '加载热门视频失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 加载指定类型的视频
     * @param {string} videoType - 视频类型
     * @param {boolean} forceRefresh - 是否强制刷新
     */
    async loadCategoryVideos(videoType, forceRefresh = false) {
      if (!videoType) {
        return;
      }

      // 检查是否已经有数据，如果有数据且不是强制刷新，则直接返回
      if (!forceRefresh && this.categoryVideos[videoType] && this.categoryVideos[videoType].length > 0) {
        return;
      }

      try {
        // 找到对应类型的ID和API调用名称
        const typeObj = this.videoTypes.find(type => type.name === videoType);
        const typeId = typeObj ? typeObj.id : null;
        const apiCategoryName = typeObj ? typeObj.categotyName : videoType;

        // 使用 getRandomVideos 获取随机视频
        const response = await getRandomVideos({
          maxResultCount: 6,
          skipCount: 0,
          videoType: apiCategoryName // 使用API调用名称
          // 对于分类视频，我们需要保留videoType参数
          // 不传递isRecommended参数，这样可以获取所有视频，而不仅仅是推荐视频
        });

        // 检查 response 是否为数组或包含 items 属性
        if ((Array.isArray(response) && response.length > 0) || (response && response.items && response.items.length > 0)) {
          const parsedData = parseVideoData(response);

          // 使用Vue.set确保响应式更新
          this.$set(this.categoryVideos, videoType, parsedData);
        } else {
          // 设置为空数组，显示暂无数据提示
          this.$set(this.categoryVideos, videoType, []);
        }
      } catch (error) {
        // 出错时设置为空数组
        this.$set(this.categoryVideos, videoType, []);

        // 显示错误提示
        uni.showToast({
          title: `加载${videoType}失败，请重试`,
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 加载推荐视频
     * @param {boolean} loadMore - 是否加载更多
     */
    async loadRecommendedVideos(loadMore = false) {
      logger.log(`加载推荐视频: loadMore=${loadMore}`);

      // 如果正在加载，则不重复加载
      if (this.recommendLoading) {
        logger.log('正在加载推荐视频，不重复加载');
        return;
      }

      // 如果是加载更多但没有更多数据，则返回
      if (loadMore && !this.recommendHasMore) {
        logger.log('没有更多推荐视频数据了');
        return;
      }

      // 如果不是加载更多，则重置页码和数据
      if (!loadMore) {
        this.recommendPage = 1;
        this.recommendedVideos = [];
        this.recommendHasMore = true;
      }


      // 猜你喜欢部分直接调用随机视频接口，不依赖热门视频的标签或类型
      // 热门视频已经在loadData中并行加载，这里不需要再次加载

      try {
        // 设置加载状态
        if (loadMore) {
          this.recommendLoading = true;
        } else {
          this.loading = true;
          this.recommendPage = 1; // 重置页码
          this.recommendHasMore = true; // 重置是否有更多数据
          this.recommendedVideos = []; // 清空数据
        }

        // 准备请求参数 - 猜你喜欢部分调用随机视频，包含所有频道
        let requestParams = {
          maxResultCount: this.recommendPageSize,
          skipCount: loadMore ? this.recommendPage * this.recommendPageSize : 0,
          sorting: 'Random'
          // 不传递videoTag和videoType参数，这样可以获取所有频道的内容
          // 不传递isRecommended参数，这样可以获取所有视频，而不仅仅是推荐视频
        };


        // 使用 getRandomVideos 获取随机推荐视频
        const response = await getRandomVideos(requestParams);

        // 检查 response 是否为数组或包含 items 属性
        if ((Array.isArray(response) && response.length > 0) || (response && response.items)) {
          const newVideos = parseVideoData(response);

          if (!newVideos || newVideos.length === 0) {
            if (!loadMore) {
              this.recommendedVideos = [];
            }
            this.recommendHasMore = false;
            return;
          }

          // 判断是否还有更多数据
          this.recommendHasMore = newVideos.length === this.recommendPageSize;

          // 更新页码
          if (loadMore && newVideos.length > 0) {
            this.recommendPage++;
            // 合并数据
            this.recommendedVideos = [...this.recommendedVideos, ...newVideos];
          } else {
            // 首次加载时替换数据
            this.recommendedVideos = newVideos;
          }


        } else {
          if (!loadMore) {
            this.recommendedVideos = [];
          }
          this.recommendHasMore = false;
        }
      } catch (error) {
        if (!loadMore) {
          this.recommendedVideos = [];
        }
        this.recommendHasMore = false;
      } finally {
        // 重置加载状态
        if (loadMore) {
          this.recommendLoading = false;
        } else {
          this.loading = false;
        }
      }
    },

    /**
     * 根据图片比例和内容高度，动态分配内容到左右两列
     * @returns {Object} 包含左右两列数据的对象
     */
    getWaterfallItems() {
      // 如果没有数据，返回空列表
      if (!this.recommendedVideos || this.recommendedVideos.length === 0) {
        return {left: [], right: []};
      }

      const left = [];
      const right = [];
      let leftHeight = 0;
      let rightHeight = 0;

      // 遍历每个视频项，分配到高度较小的列
      this.recommendedVideos.forEach(item => {
        if (!item) return;

        // 计算预估高度
        const aspectRatio = this.getImageAspectRatio(item.poster);
        const estimatedHeight = (100 / aspectRatio) + 90 + 32;

        // 分配到高度较小的列
        if (leftHeight <= rightHeight) {
          left.push(item);
          leftHeight += estimatedHeight;
        } else {
          right.push(item);
          rightHeight += estimatedHeight;
        }
      });

      return {left, right};
    },

    /**
     * 根据图片URL估算宽高比
     * @param {string} imageUrl - 图片URL
     * @returns {number} 宽高比
     */
    getImageAspectRatio(imageUrl) {
      if (!imageUrl) return 1; // 默认正方形

      // 常见图片格式快速判断
      if (imageUrl.includes('/300x400') || imageUrl.includes('300x400')) {
        return 3/4; // 海报比例
      }

      if (imageUrl.includes('/500x283') || imageUrl.includes('500x283')) {
        return 5/3; // 宽幅比例
      }

      // 从 URL 中提取宽高信息
      const match = imageUrl.match(/(\d+)x(\d+)/);
      if (match && match[1] && match[2]) {
        return parseInt(match[1]) / parseInt(match[2]);
      }

      // 根据关键字判断
      if (imageUrl.includes('video') || imageUrl.includes('movie') || imageUrl.includes('film')) {
        return 16/9; // 视频比例
      }

      return 1; // 默认正方形
    },

    /**
     * 刷新指定类型的视频数据
     * @param {string} type - 视频类型
     */
    async refreshVideos(type) {
      if (!type) {
        // 刷新所有数据
        await this.loadData();
        return;
      }

      // 创建一个加载状态标记
      const loadingKey = `${type}Loading`;
      this[loadingKey] = true;

      // 在对应类型的数据上添加一个加载中的标记，而不是清空数据
      // 这样可以在模板中显示加载中的状态，而不是空白内容
      if (this.categoryVideos[type] && this.categoryVideos[type].length > 0) {
        // 添加加载中的标记，而不是清空数据
        this.$set(this.categoryVideos, `${type}Refreshing`, true);
      }

      try {
        // 找到对应的类型对象，获取API调用名称
        const typeObj = this.videoTypes.find(videoType => videoType.name === type);
        const apiCategoryName = typeObj ? typeObj.categotyName : type;

        // 在后台加载新数据
        const response = await getRandomVideos({
          maxResultCount: 6,
          skipCount: 0,
          videoType: apiCategoryName,
          sorting: 'Random' // 确保每次都是随机的
        });

        // 处理响应数据
        if ((Array.isArray(response) && response.length > 0) || (response && response.items && response.items.length > 0)) {
          const newData = parseVideoData(response);

          // 使用动画效果平滑切换数据
          // 先设置透明度为0，然后替换数据，再恢复透明度
          this.$set(this.categoryVideos, type, newData);

          // 显示成功提示（使用较小的提示，不影响用户体验）
          uni.showToast({
            title: '已更新',
            icon: 'success',
            duration: 1000,
            position: 'bottom' // 在底部显示提示，不影响内容区域
          });
        } else {
          // 如果没有数据，设置为空数组
          this.$set(this.categoryVideos, type, []);

          // 显示提示
          uni.showToast({
            title: '暂无数据',
            icon: 'none',
            duration: 1000,
            position: 'bottom'
          });
        }
      } catch (error) {
        console.error(`刷新${type}视频失败:`, error);

        // 显示错误提示，使用较小的提示
        uni.showToast({
          title: '刷新失败',
          icon: 'none',
          duration: 1000,
          position: 'bottom'
        });
      } finally {
        // 移除加载状态标记
        this[loadingKey] = false;
        this.$set(this.categoryVideos, `${type}Refreshing`, false);
      }
    },

    /**
     * 根据类别名称查找对应的标签页索引
     * @param {string} category - 类别名称
     * @returns {number} 标签页索引
     */
    findTabIndexByCategory(category) {
      // 直接返回电影标签的索引，让首页处理具体的切换逻辑
      return 1;
    },

    /**
     * 导航到指定路径
     * @param {string} path - 路径
     */
    navigateTo(path) {
      uni.navigateTo({
        url: path
      });
    },

    /**
     * 页面滚动时触发
     * @param {Object} e - 滚动事件对象，包含 scrollTop 属性
     */
    onPageScroll(e) {
      // 获取页面高度
      const windowHeight = uni.getSystemInfoSync().windowHeight;
      const scrollTop = e.scrollTop;

      // 判断是否滚动到了页面的 80% 位置
      // 注意：这里的逻辑是估算的，可能需要根据实际情况调整
      const documentHeight = windowHeight * 1.5; // 估算文档总高度
      const scrollThreshold = documentHeight * 0.8; // 80% 的文档高度

      // 如果滚动超过了阈值，并且有更多数据可加载，则自动加载更多
      if (scrollTop > scrollThreshold && this.recommendHasMore && !this.recommendLoading) {
        // 传入true表示加载更多，不会重复加载热门视频
        this.loadRecommendedVideos(true);
      }
    },

    /**
     * 页面到达底部时触发
     */
    onReachBottom() {
      // 自动加载更多“猜你喜欢”内容
      if (this.recommendHasMore && !this.recommendLoading) {
        // 加载更多混合推荐数据
        this.loadMixedRecommendedVideos(true);

        // 显示加载提示
        uni.showToast({
          title: '加载中...',
          icon: 'loading',
          duration: 1000
        });
      } else if (!this.recommendHasMore) {
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none',
          duration: 1500
        });
      }
    },

    /**
     * 处理页面触底事件
     */
    handleReachBottom() {
      // 如果有更多数据且不在加载中，则加载更多
      if (this.recommendHasMore && !this.recommendLoading) {
        // 加载更多混合推荐数据
        this.loadMixedRecommendedVideos(true);

        // 显示加载提示
        uni.showToast({
          title: '加载中...',
          icon: 'loading',
          duration: 1000
        });
      } else if (!this.recommendHasMore) {
        // 如果没有更多数据，显示提示
        uni.showToast({
          title: '没有更多数据了',
          icon: 'none',
          duration: 1500
        });
      } else if (this.recommendLoading) {
        // 如果正在加载，显示提示
        console.log('正在加载中，忽略触底事件');
      }
    },

    /**
     * 导航到指定类别列表页（与menu.vue保持一致）
     * @param {string} category - 类别名称
     */
    navigateToTab(category) {
      // 查找对应类别的categotyId
      const typeObj = this.videoTypes.find(type => type.name === category);
      const categotyId = typeObj ? typeObj.id : null;

      // 构建URL参数
      const params = {
        videoType: category,
        videoTypeId: categotyId,
        title: category,
        sorting: 'VideoAddTime desc'
      };

      // 构建URL查询字符串
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');

      // 导航到列表页
      uni.navigateTo({
        url: `/pages/list/list?${queryString}`
      });
    },

    /**
     * 根据categotyId查找标签索引
     * @param {string} categotyId - 类别 ID
     * @returns {number} 标签索引，如果未找到返回-1
     */
    findTabIndexByCategotyId(categotyId) {
      // 这个方法需要在首页实现，这里只是一个占位符
      // 实际上，我们只需要传递categotyId，首页会处理具体的切换逻辑
      return -1; // 返回-1，让首页处理
    },

    /**
     * 随机打乱数组
     * @param {Array} array - 要打乱的数组
     * @returns {Array} 打乱后的数组
     */
    shuffleArray(array) {
      const newArray = [...array];
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
      }
      return newArray;
    },

    /**
     * 加载导航数据
     */
    async loadNavigations() {
      try {
        const response = await getCachedNavigationList();
        if (response && Array.isArray(response)) {
          this.navigations = response;
          logger.info('导航数据加载成功:', response.length);
        } else {
          logger.warn('导航数据为空或格式错误');
          this.navigations = [];
        }
      } catch (error) {
        logger.error('加载导航数据失败:', error);
        this.navigations = [];
      }
    },

    /**
     * 判断视频类型是否为横屏布局
     * @param {string} videoType - 视频类型
     * @returns {boolean} 是否为横屏布局
     */
    isHorizontalVideoType(videoType) {
      if (!videoType || !this.navigations.length) {
        return false;
      }

      // 将视频类型按逗号分割，以匹配导航数据
      const videoTypes = videoType.split(',').map(type => type.trim());

      // 查找匹配的导航项
      const currentNavItem = this.navigations.find(nav => {
        return videoTypes.some(type =>
          nav.categotyName === type ||
          nav.name === type
        );
      });

      if (currentNavItem && currentNavItem.extraParams) {
        try {
          // 先尝试解析为JSON
          if (typeof currentNavItem.extraParams === 'string') {
            const extraParamsObj = JSON.parse(currentNavItem.extraParams);
            return extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          } else {
            // 如果不是字符串，直接使用
            return currentNavItem.extraParams && currentNavItem.extraParams.isHorizontalLayout === true;
          }
        } catch (error) {
          logger.error('解析extraParams失败:', error, currentNavItem.extraParams);
          // 兼容旧版本：检查是否为 '0'
          return currentNavItem.extraParams === '0';
        }
      }

      // 特殊情况：如果视频类型包含“短剧”，直接判断为横屏
      if (videoTypes.some(type => type.includes('短剧'))) {
        return true;
      }

      return false;
    },

    /**
     * 判断某个category是否为横屏类型
     * @param {string} category - 类别名称
     * @returns {boolean} 是否为横屏类型
     */
    isCategoryHorizontal(category) {
      if (!category || !this.videoTypes.length) {
        return false;
      }

      // 找到对应的类型对象
      const typeObj = this.videoTypes.find(type => type.name === category);
      if (!typeObj) {
        return false;
      }

      // 使用categotyName来判断是否为横屏
      return this.isHorizontalVideoType(typeObj.categotyName);
    },

    /**
     * 加载混合推荐视频（10个横屏 + 9个竖屏为一组）
     * @param {boolean} loadMore - 是否加载更多
     */
    async loadMixedRecommendedVideos(loadMore = false) {
      logger.info(`加载混合推荐视频: loadMore=${loadMore}`);

      // 如果正在加载，则不重复加载
      if (this.recommendLoading) {
        logger.info('正在加载混合推荐视频，不重复加载');
        return;
      }

      // 如果是加载更多但没有更多数据，则返回
      if (loadMore && !this.recommendHasMore) {
        logger.info('没有更多混合推荐视频数据了');
        return;
      }

      // 如果不是加载更多，则重置数据
      if (!loadMore) {
        this.recommendPage = 1;
        this.horizontalVideos = [];
        this.verticalVideos = [];
        this.mixedGroups = [];
        this.recommendHasMore = true;
      }

      try {
        // 设置加载状态
        if (loadMore) {
          this.recommendLoading = true;
        } else {
          this.loading = true;
        }

        // 并行加载横屏和竖屏视频
        const [horizontalVideos, verticalVideos] = await Promise.all([
          this.loadHorizontalVideos(10), // 加载10个横屏视频
          this.loadVerticalVideos(8)     // 加载9个竖屏视频
        ]);

        // 检查是否有数据
        if (horizontalVideos.length === 0 && verticalVideos.length === 0) {
          if (!loadMore) {
            this.mixedGroups = [];
          }
          this.recommendHasMore = false;
          return;
        }

        // 创建混合组合
        const newGroup = {
          horizontal: horizontalVideos,
          vertical: verticalVideos
        };

        // 判断是否还有更多数据
        this.recommendHasMore = horizontalVideos.length === 10 || verticalVideos.length === 8;

        // 更新数据
        if (loadMore) {
          this.mixedGroups.push(newGroup);
        } else {
          this.mixedGroups = [newGroup];
        }

        logger.info(`混合推荐视频加载成功: 横屏${horizontalVideos.length}个, 竖屏${verticalVideos.length}个`);

      } catch (error) {
        logger.error('加载混合推荐视频失败:', error);
        if (!loadMore) {
          this.mixedGroups = [];
        }
        this.recommendHasMore = false;
      } finally {
        // 重置加载状态
        if (loadMore) {
          this.recommendLoading = false;
        } else {
          this.loading = false;
        }
      }
    },

    /**
     * 加载横屏视频
     * @param {number} count - 需要加载的数量
     * @returns {Array} 横屏视频数组
     */
    async loadHorizontalVideos(count = 10) {
      try {
        // 获取横屏视频类型
        const horizontalCategories = this.navigations
          .filter(nav => {
            if (!nav.isH5Display) return false;

            // 解析 extraParams
            let extraParamsObj = null;
            try {
              if (nav.extraParams && typeof nav.extraParams === 'string') {
                extraParamsObj = JSON.parse(nav.extraParams);
              }
            } catch (error) {
              logger.error('解析extraParams失败:', error, nav.extraParams);
              return false;
            }

            // 筛选 isHorizontalLayout 为 true 的项目
            return extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          })
          .map(nav => nav.categotyName);

        if (horizontalCategories.length === 0) {
          logger.warn('没有找到横屏视频类型');
          return [];
        }

        // 随机选择一个横屏类型
        const randomCategory = horizontalCategories[Math.floor(Math.random() * horizontalCategories.length)];

        // 加载横屏视频
        const response = await getRandomVideos({
          maxResultCount: count,
          skipCount: 0,
          videoType: randomCategory,
          sorting: 'Random'
        });

        if (this.isValidResponse(response)) {
          const videos = parseVideoData(response);
          logger.info(`加载横屏视频成功: ${videos.length}个, 类型: ${randomCategory}`);
          return videos;
        }

        return [];
      } catch (error) {
        logger.error('加载横屏视频失败:', error);
        return [];
      }
    },

    /**
     * 加载竖屏视频
     * @param {number} count - 需要加载的数量
     * @returns {Array} 竖屏视频数组
     */
    async loadVerticalVideos(count = 89) {
      try {
        // 获取竖屏视频类型（排除横屏类型）
        const verticalCategories = this.navigations
          .filter(nav => {
            if (!nav.isH5Display) return false;

            // 解析 extraParams
            let extraParamsObj = null;
            try {
              if (nav.extraParams && typeof nav.extraParams === 'string') {
                extraParamsObj = JSON.parse(nav.extraParams);
              }
            } catch (error) {
              // 如果解析失败，认为是竖屏类型
              return true;
            }

            // 筛选 isHorizontalLayout 不为 true 的项目
            return !(extraParamsObj && extraParamsObj.isHorizontalLayout === true);
          })
          .map(nav => nav.categotyName);

        if (verticalCategories.length === 0) {
          logger.warn('没有找到竖屏视频类型');
          return [];
        }

        // 随机选择一个竖屏类型
        const randomCategory = verticalCategories[Math.floor(Math.random() * verticalCategories.length)];

        // 加载竖屏视频
        const response = await getRandomVideos({
          maxResultCount: count,
          skipCount: 0,
          videoType: randomCategory,
          sorting: 'Random'
        });

        if (this.isValidResponse(response)) {
          const videos = parseVideoData(response);
          logger.info(`加载竖屏视频成功: ${videos.length}个, 类型: ${randomCategory}`);
          return videos;
        }

        return [];
      } catch (error) {
        logger.error('加载竖屏视频失败:', error);
        return [];
      }
    }
  }
}
</script>

<style scoped>
.title-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #fff;
}

.more {
  margin: 40rpx 0;
  display: flex;
  justify-content: space-around;
  background: #242424;
  position: relative;
  align-items: center;
  height: 80rpx;

  span {
    display: flex;
    align-items: center;
    transition: opacity 0.3s ease;
  }

  span.refreshing {
    opacity: 0.7;
    cursor: not-allowed;
  }

  span.line {
    background: #ffffff30;
    display: block;
    width: 2rpx;
    height: 60%;
    position: absolute;
  }

  /* 旋转动画 */
  @keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .rotating {
    animation: rotate 1s linear infinite;
    display: inline-block;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 116, 61, 0.2);
  border-top-color: #ff743d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969699;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #969699;
  text-align: center;
  margin-bottom: 20rpx;
}

.error-retry {
  padding: 10rpx 30rpx;
  background: rgba(255, 116, 61, 0.1);
  border: 1px solid #ff743d;
  border-radius: 30rpx;
  color: #ff743d;
  font-size: 24rpx;
}



/* 调试信息样式 */
.debug-info {
  background: rgba(255, 0, 0, 0.8);
  color: #fff;
  padding: 20rpx;
  margin: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.8;
  font-weight: bold;
  z-index: 9999;
  position: relative;
}

/* 无数据提示样式 */
.no-data-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #969699;
  font-size: 28rpx;
  height: 200rpx;
  width: 100%;
}

.no-data-tip text {
  color: #969699;
}

.no-data-tip .retry-button {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background: rgba(255, 116, 61, 0.1);
  border: 1px solid #ff743d;
  border-radius: 30rpx;
  color: #ff743d;
  font-size: 24rpx;
}

/* 加载更多按钮样式 */
.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  background: #242424;
}

.load-more-btn {
  padding: 15rpx 40rpx;
  background-color: #ff743d;
  color: #fff;
  border-radius: 50rpx;
  font-size: 28rpx;
  text-align: center;
}

.no-more-data {
  color: #969699;
  font-size: 28rpx;
  text-align: center;
}

.loading {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-hint {
  color: #969699;
  font-size: 28rpx;
  text-align: center;
  padding: 20rpx 0;
}



/* 混合布局样式 */
.mixed-group {
  margin-bottom: 10rpx;
}

.horizontal-section,
.vertical-section {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
}

.section-title:before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #fe748e, #ff743d);
  border-radius: 3rpx;
  margin-right: 16rpx;
}
</style>