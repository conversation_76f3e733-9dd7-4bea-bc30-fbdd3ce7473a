<template>
  <!-- H5环境使用img标签，完全参考PC端实现 -->
  <!-- #ifdef H5 -->
  <img ref="imageRef" :src="imageUrl" :alt="alt" style="width: 100%; height: 100%; object-fit: cover;" />
  <!-- #endif -->

  <!-- 非H5环境使用image标签 -->
  <!-- #ifndef H5 -->
  <image
    ref="imageRef"
    :src="processedImageUrl || imageUrl"
    mode="aspectFill"
    :alt="alt"
    style="width: 100%; height: 100%;"
    @error="handleImageError"
    @load="handleImageLoad"
  />
  <!-- #endif -->
</template>

<script>
export default {
  name: 'ImageRestore',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processedImageUrl: ''
    }
  },
  mounted() {
    // 非H5环境下先设置原始图片，确保能立即显示
    // #ifndef H5
    if (this.imageUrl) {
      this.processedImageUrl = this.imageUrl;
    }
    // #endif

    if (this.imageUrl && this.$refs.imageRef) {
      this.fetchImageAndRemoveFirstFourBytes(this.imageUrl, this.$refs.imageRef);
    }
  },
  watch: {
    imageUrl(newUrl) {
      if (newUrl && this.$refs.imageRef) {
        // 清理之前的处理结果
        // #ifndef H5
        this.processedImageUrl = newUrl;
        // #endif
        this.fetchImageAndRemoveFirstFourBytes(newUrl, this.$refs.imageRef);
      }
    }
  },
  methods: {
    async fetchImageAndRemoveFirstFourBytes(imageUrl, imgElement) {
      if (!imageUrl || !imgElement) {
        return;
      }

      try {
        // #ifdef H5
        // H5环境下使用与PC端完全相同的处理逻辑
        const response = await fetch(imageUrl, { cache: 'default' });
        if (!response.ok) {
          throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        const newBuffer = buffer.slice(4);
        const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
        const objectURL = URL.createObjectURL(blob);

        imgElement.src = objectURL;
        imgElement.onload = () => {
          URL.revokeObjectURL(objectURL);
        };
        // #endif

        // #ifndef H5
        // 非H5环境下，尝试使用uni.request处理图片
        const self = this;
        uni.request({
          url: imageUrl,
          method: 'GET',
          responseType: 'arraybuffer',
          success(res) {
            try {
              if (res.statusCode === 200 && res.data) {
                // 移除前4个字节
                const buffer = res.data;
                const newBuffer = buffer.slice(4);

                // 在非H5环境下，将ArrayBuffer转换为base64
                const base64 = uni.arrayBufferToBase64(newBuffer);
                const dataUrl = `data:image/jpeg;base64,${base64}`;

                self.processedImageUrl = dataUrl;
              } else {
                console.warn('图片请求失败，使用原始图片');
                self.processedImageUrl = imageUrl;
              }
            } catch (error) {
              console.error('处理图片数据失败:', error);
              self.processedImageUrl = imageUrl;
            }
          },
          fail(error) {
            console.error('图片请求失败:', error);
            self.processedImageUrl = imageUrl;
          }
        });
        // #endif

      } catch (error) {
        console.error('处理图片时发生错误:', error);
        // #ifndef H5
        this.processedImageUrl = imageUrl;
        // #endif
      }
    },

    // 图片加载成功
    handleImageLoad() {
      console.log('图片加载成功:', this.imageUrl);
    },

    // 图片加载失败
    handleImageError(e) {
      console.error('图片加载失败:', this.imageUrl, e);
      // 在非H5环境下，如果处理后的图片加载失败，尝试使用原始图片
      // #ifndef H5
      if (this.processedImageUrl !== this.imageUrl) {
        console.log('回退到原始图片URL');
        this.processedImageUrl = this.imageUrl;
      }
      // #endif
    }
  }
}
</script>

<style scoped></style>
