```vue
<template>
  <image ref="imageRef" :src="imageSrc" :alt="alt" @load="onImageLoad" @error="onImageError" mode="aspectFill" />
</template>

<script>
export default {
  name: 'ImageRestore',
  props: {
    imageUrl: {
      type: String,
      required: true,
      default: ''
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imageSrc: '' // 用于存储动态生成的 Blob URL
    }
  },
  mounted() {
    this.fetchImageAndRemoveFirstFourBytes(this.imageUrl);
  },
  watch: {
    imageUrl(newUrl) {
      this.fetchImageAndRemoveFirstFourBytes(newUrl);
    }
  },
  methods: {
    async fetchImageAndRemoveFirstFourBytes(imageUrl) {
      if (!imageUrl) {
        console.warn('图片 URL 为空');
        return;
      }
      try {
        // 发起请求获取图片数据
        const response = await fetch(imageUrl, { cache: 'default' });
        if (!response.ok) {
          throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
        }

        // 将响应转换为 ArrayBuffer
        const buffer = await response.arrayBuffer();

        // 创建一个新的 ArrayBuffer，排除前四个字节
        const newBuffer = buffer.slice(4);

        // 将处理后的数据转换为 Blob
        const contentType = response.headers.get('content-type') || 'image/jpeg';
        const blob = new Blob([newBuffer], { type: contentType });

        // 释放之前的 Blob URL（如果存在）
        if (this.imageSrc) {
          URL.revokeObjectURL(this.imageSrc);
        }

        // 创建新的 Blob URL 并设置为 imageSrc
        this.imageSrc = URL.createObjectURL(blob);
      } catch (error) {
        console.error('处理图片时发生错误:', error);
        this.imageSrc = ''; // 清空图片源以避免显示错误
      }
    },
    onImageLoad() {
      console.log('图片加载成功');
      // 可选择在此释放 Blob URL，但建议在组件销毁时处理
    },
    onImageError(error) {
      console.error('图片加载失败:', error);
    }
  },
  beforeDestroy() {
    // 组件销毁时释放 Blob URL
    if (this.imageSrc) {
      URL.revokeObjectURL(this.imageSrc);
    }
  }
}
</script>

<style scoped>

</style>
