<template>
  <image
    ref="imageRef"
    :src="imageUrl"
    mode="aspectFill"
    :alt="alt"
    style="width: 100%; height: 100%;"
    @error="handleImageError"
    @load="handleImageLoad"
  />
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
})

// 图片加载成功
const handleImageLoad = () => {
  console.log('图片加载成功:', props.imageUrl);
};

// 图片加载失败
const handleImageError = (e: any) => {
  console.error('图片加载失败:', props.imageUrl, e);
};
</script>

<style scoped></style>
