<template>
  <image ref="imageRef" :src="processedImageUrl" mode="aspectFill" :alt="props.alt" style="width: 100%; height: 100%;" />
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
})

const imageRef = ref<any>(null);
const processedImageUrl = ref('');

const fetchImageAndRemoveFirstFourBytes = async (imageUrl: string): Promise<void> => {
  if(!imageUrl){
    processedImageUrl.value = '';
    return
  }

  try {
    // 先设置原图片URL，保证图片能显示
    processedImageUrl.value = imageUrl;

    // 尝试处理图片数据（只在支持fetch的环境下）
    if (typeof fetch !== 'undefined') {
      const response = await fetch(imageUrl, { cache: 'default' });
      if (!response.ok) {
        console.warn(`图片加载失败: ${response.status} ${response.statusText}`);
        return;
      }

      const buffer = await response.arrayBuffer();
      const newBuffer = buffer.slice(4);
      const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
      const objectURL = URL.createObjectURL(blob);

      // 更新为处理后的图片URL
      processedImageUrl.value = objectURL;

      // 清理内存
      setTimeout(() => {
        URL.revokeObjectURL(objectURL);
      }, 5000);
    }

  } catch (error) {
    console.error('处理图片时发生错误:', error);
    // 出错时保持使用原图片URL
  }
}

onMounted(() => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl)
});

watch(() => props.imageUrl, () => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl)
});
</script>

<style scoped></style>
