<template>
  <image
    ref="imageRef"
    :src="processedImageUrl || props.imageUrl"
    mode="aspectFill"
    :alt="props.alt"
    style="width: 100%; height: 100%;"
    @error="handleImageError"
    @load="handleImageLoad"
  />
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true
  },
  alt: {
    type: String,
    default: ''
  },
})

const imageRef = ref<any>(null);
const processedImageUrl = ref('');

const fetchImageAndRemoveFirstFourBytes = async (imageUrl: string, imgElement: any): Promise<void> => {
  if (!imageUrl || !imgElement) {
    return;
  }

  try {
    // #ifdef H5
    // H5环境下使用与PC端相同的处理逻辑
    const response = await fetch(imageUrl, { cache: 'default' });
    if (!response.ok) {
      throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const newBuffer = buffer.slice(4);
    const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
    const objectURL = URL.createObjectURL(blob);

    imgElement.src = objectURL;
    imgElement.onload = () => {
      URL.revokeObjectURL(objectURL);
    };
    // #endif

    // #ifndef H5
    // 非H5环境下，尝试使用uni.request处理图片
    uni.request({
      url: imageUrl,
      method: 'GET',
      responseType: 'arraybuffer',
      success: (res: any) => {
        try {
          if (res.statusCode === 200 && res.data) {
            // 移除前4个字节
            const buffer = res.data;
            const newBuffer = buffer.slice(4);

            // 在非H5环境下，将ArrayBuffer转换为base64
            const base64 = uni.arrayBufferToBase64(newBuffer);
            const dataUrl = `data:image/jpeg;base64,${base64}`;

            processedImageUrl.value = dataUrl;
          } else {
            console.warn('图片请求失败，使用原始图片');
            processedImageUrl.value = imageUrl;
          }
        } catch (error) {
          console.error('处理图片数据失败:', error);
          processedImageUrl.value = imageUrl;
        }
      },
      fail: (error: any) => {
        console.error('图片请求失败:', error);
        processedImageUrl.value = imageUrl;
      }
    });
    // #endif

  } catch (error) {
    console.error('处理图片时发生错误:', error);
    // #ifndef H5
    processedImageUrl.value = imageUrl;
    // #endif
  }
}

// 图片加载成功
const handleImageLoad = () => {
  console.log('图片加载成功:', props.imageUrl);
};

// 图片加载失败
const handleImageError = (e: any) => {
  console.error('图片加载失败:', props.imageUrl, e);
  // 在非H5环境下，如果处理后的图片加载失败，尝试使用原始图片
  // #ifndef H5
  if (processedImageUrl.value !== props.imageUrl) {
    console.log('回退到原始图片URL');
    processedImageUrl.value = props.imageUrl;
  }
  // #endif
};

onMounted(() => {
  if (props.imageUrl && imageRef.value) {
    fetchImageAndRemoveFirstFourBytes(props.imageUrl, imageRef.value);
  }
});

watch(() => props.imageUrl, (newUrl) => {
  if (newUrl && imageRef.value) {
    // 清理之前的处理结果
    processedImageUrl.value = '';
    fetchImageAndRemoveFirstFourBytes(newUrl, imageRef.value);
  }
});
</script>

<style scoped></style>
