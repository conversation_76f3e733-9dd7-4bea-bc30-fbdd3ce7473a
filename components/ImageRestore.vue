<template>
  <!-- H5环境使用img标签 -->
  <!-- #ifdef H5 -->
  <img ref="imageRef" :alt="props.alt" style="width: 100%; height: 100%; object-fit: cover;" />
  <!-- #endif -->

  <!-- 非H5环境使用image标签 -->
  <!-- #ifndef H5 -->
  <image ref="imageRef" :src="processedImageUrl" mode="aspectFill" :alt="props.alt" style="width: 100%; height: 100%;" />
  <!-- #endif -->
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch, computed } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
})

const imageRef = ref<any>(null);
const processedImageUrl = ref('');

const fetchImageAndRemoveFirstFourBytes = async (imageUrl: string): Promise<void> => {
  if(!imageUrl){
    return
  }

  try {
    // #ifdef H5
    // H5环境下的处理
    const response = await fetch(imageUrl, { cache: 'default' });
    if (!response.ok) {
      throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const newBuffer = buffer.slice(4);
    const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
    const objectURL = URL.createObjectURL(blob);

    if (imageRef.value) {
      imageRef.value.src = objectURL;
      imageRef.value.onload = () => {
        URL.revokeObjectURL(objectURL);
      };
    }
    // #endif

    // #ifndef H5
    // 非H5环境直接使用原图片URL
    processedImageUrl.value = imageUrl;
    // #endif

  } catch (error) {
    console.error('处理图片时发生错误:', error);
    // 出错时使用原图片URL
    // #ifdef H5
    if (imageRef.value) {
      imageRef.value.src = imageUrl;
    }
    // #endif
    // #ifndef H5
    processedImageUrl.value = imageUrl;
    // #endif
  }
}

onMounted(() => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl)
});

watch(() => props.imageUrl, () => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl)
});
</script>

<style scoped></style>
