<template>
  <img ref="imageRef" v-bind="props" :alt="props.alt" />
</template>

<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String || undefined,
    required: true,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
})

const imageRef = ref<HTMLImageElement | null>(null);

const fetchImageAndRemoveFirstFourBytes = async (imageUrl: string, imgElement: HTMLImageElement): Promise<void> => {
  if(!imageUrl|| !imgElement){
    return
  }
  try {
    // 发起请求获取图片数据
    const response = await fetch(imageUrl, { cache: 'default' });
    if (!response.ok) {
      throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为ArrayBuffer
    const buffer = await response.arrayBuffer();

    // 创建一个新的ArrayBuffer，排除前四个字节
    const newBuffer = buffer.slice(4);

    // 将处理后的数据转换为Blob
    const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });

    // 创建URL并设置到img元素的src属性
    const objectURL = URL.createObjectURL(blob);
    imgElement.src = objectURL;

    // 当图片加载完成后，释放创建的URL对象
    imgElement.onload = () => {
      URL.revokeObjectURL(objectURL);
    };
  } catch (error) {
    console.error('处理图片时发生错误:', error);
  }
}

onMounted(() => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl, imageRef.value!)
});

watch(() => props.imageUrl, () => {
  fetchImageAndRemoveFirstFourBytes(props.imageUrl, imageRef.value!)
});
</script>

<style scoped></style>
